import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import CardBack from "./CardBack";
import { Card as GameCard } from "@/types/game";

interface PlayingCardProps {
  suit: "hearts" | "diamonds" | "clubs" | "spades";
  value: string;
  isHeld: boolean;
  isDealt: boolean;
  showBack?: boolean;
  index: number;
  onToggleHold: (index: number) => void;
}

interface CardHandProps {
  cards: GameCard[];
  canHold?: boolean;
  showBacks?: boolean;
  onCardClick?: (index: number) => void;
}

const PlayingCard: React.FC<PlayingCardProps> = ({
  suit,
  value,
  isHeld,
  isDealt,
  showBack = false,
  index,
  onToggleHold,
}) => {
  const suitSymbol = {
    hearts: "♥",
    diamonds: "♦",
    clubs: "♣",
    spades: "♠",
  };

  const suitColor =
    suit === "hearts" || suit === "diamonds" ? "text-red-600" : "text-black";

  if (showBack || !isDealt) {
    return (
      <div className="relative">
        <CardBack onClick={() => onToggleHold(index)} />
      </div>
    );
  }

  return (
    <div className="relative">
      <Card
        className={`w-32 h-44 flex flex-col justify-between p-2 bg-white border-2 cursor-pointer transition-all duration-200 ${
          isHeld ? "border-yellow-400 shadow-lg transform -translate-y-2" : "border-gray-200 hover:border-gray-400"
        }`}
        onClick={() => onToggleHold(index)}
      >
        <CardContent className="p-0 flex flex-col h-full justify-between">
          <div className={`text-lg font-bold ${suitColor}`}>
            <div className="flex flex-col items-start">
              <span>{value}</span>
              <span className="text-xl">{suitSymbol[suit]}</span>
            </div>
          </div>
          <div className={`text-4xl ${suitColor} self-center`}>
            {suitSymbol[suit]}
          </div>
          <div className={`text-lg font-bold ${suitColor} self-end rotate-180`}>
            <div className="flex flex-col items-start">
              <span>{value}</span>
              <span className="text-xl">{suitSymbol[suit]}</span>
            </div>
          </div>
        </CardContent>
      </Card>
      {isHeld && (
        <div className="absolute -bottom-6 left-0 right-0 flex justify-center">
          <span className="bg-yellow-400 text-black text-xs font-bold px-2 py-1 rounded-md">
            HELD
          </span>
        </div>
      )}
    </div>
  );
};

const CardHand: React.FC<CardHandProps> = ({
  cards,
  canHold = true,
  showBacks = false,
  onCardClick = () => {},
}) => {
  const handleCardClick = (index: number) => {
    if (!canHold) return;
    onCardClick(index);
  };

  return (
    <div className="bg-green-800 p-6 rounded-lg">
      <div className="flex justify-center gap-4 mb-4">
        {cards.map((card: GameCard, index: number) => (
          <PlayingCard
            key={card.id || index}
            suit={card.suit}
            value={card.value}
            isHeld={card.isHeld}
            isDealt={card.isDealt}
            showBack={showBacks}
            index={index}
            onToggleHold={handleCardClick}
          />
        ))}
      </div>
      {canHold && (
        <div className="text-center text-white text-sm mt-2">
          Select cards to hold before drawing. Good luck!
        </div>
      )}
    </div>
  );
};

export default CardHand;
